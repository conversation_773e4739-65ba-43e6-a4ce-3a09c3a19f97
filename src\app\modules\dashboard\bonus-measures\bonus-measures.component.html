<!-- Bonus Measures Dashboard -->
<div class="report-wrapper">
  <!-- Report Header -->
  <div class="report-header">
    <div class="header-left">
      <h1 class="report-title">Bonus Measures</h1>
    </div>
  </div>

  <!-- Report Content -->
  <div class="report-content-layout">
    <!-- Main Content Area -->
    <div class="report-viewer-area">
      <!-- Main Bonus Measures Table -->
      <div
        class="bonus-measures-container"
        [style.display]="showReportDetails ? 'none' : 'block'">
        <div class="header">
          <div class="header-content">
            <div class="container-fluid">
              <div class="row gx-0 align-items-end">
                <div class="col-sm-10 text-center">
                  <div>AHF Bonus</div>
                  <div>Active patients who have been seen at an HCC clinic.</div>
                  <span class="qualifying-patients">{{ getTotalMeasuresSum() | number }} Qualifying Patients: Measure Year 2025</span>
                </div>
                <div class="col-sm-2 export-dropdown text-end">
                  <app-download-menu
                    (printClicked)="printTable()"
                    (pdfExportClicked)="downloadPDF()"
                    (excelExportClicked)="downloadExcel()"
                    (csvExportClicked)="downloadCSV()">
                  </app-download-menu>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tabulator Table Container -->
        <div class="table-container">
          <div id="bonus-measures-table"></div>
        </div>
      </div>

      <!-- Report Details Component -->
      <div
        class="report-details-container"
        [style.display]="showReportDetails ? 'block' : 'none'">
        <app-report-details
          [siteId]="selectedRowData?.siteId"
          [year]="selectedRowData?.year"
          [cohortId]="selectedRowData?.cohortId"
          [locationCd]="selectedRowData?.locationCd"
          [providerCd]="selectedRowData?.providerCd"
          [rollingWeek]="selectedRowData?.rollingWeek"
          [alertLvl]="selectedRowData?.alertLvl"
          [measuresCd]="selectedRowData?.measuresCd"
          [guidelineDesc]="selectedRowData?.guidelineDesc"
          (backClicked)="closeReportDetails()">
        </app-report-details>
      </div>
    </div>
  </div>
</div>
