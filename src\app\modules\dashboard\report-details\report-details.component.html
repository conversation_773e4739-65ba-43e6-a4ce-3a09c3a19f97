<!-- Report Details Component -->
<div class="report-details-wrapper">
  <!-- Header -->
  <div class="header">
    <div class="header-content">
      <span class="query-information">{{ getQueryInformation() }}</span>
      <span class="patient-count">{{ getTotalPatients() | number }} Patients</span>
      <div class="export-dropdown">
        <app-download-menu
          (printClicked)="printTable()"
          (pdfExportClicked)="exportToPDF()"
          (excelExportClicked)="exportToExcel()"
          (csvExportClicked)="exportToCSV()">
        </app-download-menu>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <div class="loading-content">
      <ngx-spinner bdColor="rgba(0, 0, 0, 0.1)" size="medium" color="#0071BC" type="ball-scale-multiple">
        <p style="color: #0071BC; margin-top: 10px;">Loading report details...</p>
      </ngx-spinner>
    </div>
  </div>

  <!-- Tabulator Table Container -->
  <div class="table-container" *ngIf="!isLoading && hasData">
    <div id="report-details-table"></div>
  </div>

  <!-- No Data State -->
  <div class="no-data-container" *ngIf="!isLoading && !hasData">
    <div class="no-data-content">
      <mat-icon class="no-data-icon">info</mat-icon>
      <h3>No Data Available</h3>
      <p>No report details found for the selected criteria.</p>
    </div>
  </div>


</div>
