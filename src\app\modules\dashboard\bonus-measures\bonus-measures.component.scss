// Quality Measures Dashboard with Left Stacked Panels
.report-wrapper {
  display: flex;
  flex-direction: column;
  height: 96vh;
  font-family: MuseoSans-300;
  background-color: #eff1f6;
  margin-top: -20px;
}

// Report Header
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: transparent;
  border-bottom: 1px solid #e0e0e0;
  z-index: 10;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .report-title {
      color: #0071BC;
      font-family: Museo500-Regular;
      margin: 0;
      font-size: 1.75rem;
      font-weight: 500;
    }
  }
}

// Report Content Layout
.report-content-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  padding: 8px;
}

// Report Viewer Area (Right)
.report-viewer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff1f6; // Match main background color
}

// Bonus Measures Specific Styles
.bonus-measures-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}


.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  margin-left: 10px;
  margin-right: 10px;
  flex-shrink: 0; // Don't shrink the header

  .header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .cohort-name {
      font-size: 1.3rem;
      font-weight: 700;
      color: #0071bc;
      text-align: center;
    }

    .cohort-description {
        font-size: 1.1rem;
        color: #777777;
    }

    .qualifying-patients {
      font-size: 0.9rem;
      color: #666;
      text-align: center;
    }

    .export-dropdown {
      position: absolute;
      right: 0;
    }
  }
}

.table-container {
  flex: 1;
  overflow: hidden;
  border-radius: 12px;
  background-color: #f9f9f9;
}

// Table and icon styles
.filled-satisfied-icon {
  color: #99d2f8;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}

.filled-unsatisfied-icon {
  color: #0089e3;
  font-size: 10px;
  text-align: right;
  vertical-align: middle !important;
  padding-top: 5px;
  padding-right: 3px;
}

.measures-table {
  width: 100%;
  border-collapse: collapse;
  overflow: hidden;
}

.number {
  text-align: right;
  padding-right: 20px;
}

/* Report Details Container */
.report-details-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.details-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}
